import {
	ActionIcon,
	Button,
	Divider,
	Group,
	Modal,
	ScrollArea,
	Stack,
	Text,
	Title,
	Tooltip,
	Image,
	SimpleGrid,
	Card,
} from "@mantine/core";
import CustomQuill from "../quill/CustomQuill";
import { useForm } from "@mantine/form";
import { useCallback, useEffect, useRef, useState } from "react";
import { IconPhoto, IconX } from "@tabler/icons-react";
import { notifications } from "@mantine/notifications";
import { AnimatePresence, motion } from "framer-motion";
import unavailableImage from "@assets/unavailable-image.png";
import { GalleryViewer } from "../GalleryViewer";
import { useHover } from "@mantine/hooks";
import { isAxiosError } from "axios";
import FullScreenLoader from "../FullScreenLoader";
import apiClient from "../../config/axios";
import "quill-mention/autoregister";
import type { EventUser } from "../../types";

type EventPostFormModalProps = {
	opened: boolean;
	onClose: () => void;
	postFormData?: {
		content: string;
		media: { S3Key: string; key: string; type: string }[];
	};
	eventId: string;
};

const defaultCustomModules = {
	toolbar: [
		"bold",
		"italic",
		"underline",
		"strike",
		{ list: "ordered" },
		{ list: "bullet" },
		"link",
		{ color: [], background: [] },
		"clean",
	],
};

const ImageItem = ({
	url,
	index,
	onRemove,
	onPreview,
}: {
	url: string;
	index: number;
	onRemove: (index: number) => void;
	onPreview: (index: number) => void;
}) => {
	const { hovered, ref } = useHover();

	return (
		<motion.div
			ref={ref}
			layout
			initial={{ opacity: 0, scale: 1 }}
			animate={{
				opacity: 1,
				scale: 1,
				transition: {
					duration: 0.3,
					delay: 0.1 * index,
				},
			}}
			exit={{ opacity: 0, scale: 0.95 }}
		>
			<Card p={"0"} shadow="md" withBorder radius="md">
				<Image
					src={url}
					alt={`Preview ${index}`}
					fallbackSrc={unavailableImage}
					h="100%"
					w="100%"
					styles={{
						root: {
							aspectRatio: "185/100",
						},
					}}
					fit="cover"
					radius={"md"}
					style={{
						filter: hovered
							? "brightness(90%)"
							: "brightness(100%)",
						cursor: "pointer",
					}}
					onClick={() => onPreview(index)}
				/>

				<ActionIcon
					color="red"
					radius="xl"
					size="md"
					onClick={() => onRemove(index)}
					style={{
						position: "absolute",
						top: 4,
						right: 4,
						opacity: hovered ? 1 : 0,
						transition: "opacity 0.2s",
						pointerEvents: hovered ? "auto" : "none",
					}}
				>
					<IconX size={16} />
				</ActionIcon>
			</Card>
		</motion.div>
	);
};

const EventPostFormModal = (props: EventPostFormModalProps) => {
	const { opened, onClose, postFormData, eventId } = props;
	const fileInputRef = useRef<HTMLInputElement>(null);

	const [loading, setLoading] = useState<boolean>(false);
	const [files, setFiles] = useState<File[]>([]);
	const [previewUrls, setPreviewUrls] = useState<string[]>([]);
	const [previewKeys, setPreviewKeys] = useState<string[]>([]);
	const [viewerOpened, setViewerOpened] = useState<number | null>(null);

	const [uploadedMedia, setUploadedMedia] = useState<
		{ S3Key: string; key: string; type: string }[]
	>([]);

	const handlePreview = useCallback((index: number) => {
		setViewerOpened(index);
	}, []);

	const form = useForm({
		initialValues: {
			content: postFormData?.content || "",
		},
	});

	const getSignedUrl = async ({
		eventId,
		fileName,
		fileType,
	}: {
		eventId: string;
		fileName: string;
		fileType: string;
	}) => {
		const response = await apiClient.get(
			`/api/events/signed-url/event-post/${eventId}`,
			{
				params: {
					fileName,
					fileType,
				},
			}
		);
		return response.data;
	};

	const handleUpload = async (filesToUpload: File[]) => {
		try {
			const uploadPromises = filesToUpload.map(async file => {
				const { uploadUrl, S3Key } = await getSignedUrl({
					eventId,
					fileName: file.name,
					fileType: file.type,
				});

				await fetch(uploadUrl, {
					method: "PUT",
					headers: {
						"Content-Type": file.type,
					},
					body: file,
				});

				const fileType: "image" | "video" = file.type.startsWith(
					"image/"
				)
					? "image"
					: "video";

				return { S3Key, key: file.name, type: fileType };
			});

			const newMedia = await Promise.all(uploadPromises);
			setUploadedMedia([...uploadedMedia, ...newMedia]);
		} catch (error) {
			console.error("File upload error:", error);
			if (isAxiosError(error)) {
				notifications.show({
					title: "File Upload Failed",
					message: error.response?.data?.message || error.message,
					color: "red",
				});
				return;
			}
			notifications.show({
				title: "File Upload Failed",
				message:
					"There was an error uploading your files. Please try again.",
				color: "red",
			});
		}
	};

	const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		const selectedFiles = event.target.files;
		if (selectedFiles) {
			if (files.length + selectedFiles.length > 25) {
				notifications.show({
					title: "File Limit Reached",
					message: "You can only upload a maximum of 25 files.",
					color: "red",
				});
				return;
			}

			const newFiles = Array.from(selectedFiles);
			setFiles([...files, ...newFiles]);
			const newPreviewUrls = newFiles.map(file =>
				URL.createObjectURL(file)
			);
			setPreviewUrls([...previewUrls, ...newPreviewUrls]);

			// Generate unique keys for new files
			const newKeys = newFiles.map(
				() =>
					`preview-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
			);
			setPreviewKeys([...previewKeys, ...newKeys]);
			handleUpload(newFiles);
		}
	};

	const handleRemoveFile = (index: number) => {
		const newFiles = [...files];
		newFiles.splice(index, 1);
		setFiles(newFiles);

		const newPreviewUrls = [...previewUrls];
		URL.revokeObjectURL(newPreviewUrls[index]);
		newPreviewUrls.splice(index, 1);
		setPreviewUrls(newPreviewUrls);

		const newPreviewKeys = [...previewKeys];
		newPreviewKeys.splice(index, 1);
		setPreviewKeys(newPreviewKeys);

		const newUploadedMedia = [...uploadedMedia];
		newUploadedMedia.splice(index, 1);
		setUploadedMedia(newUploadedMedia);

		if (fileInputRef.current) {
			fileInputRef.current.value = "";
		}
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		try {
			setLoading(true);
			const response = await apiClient.post(
				`/api/events/create-event-post/${eventId}`,
				{
					content: form.values.content,
					mediaFiles: uploadedMedia,
				}
			);
			notifications.show({
				title: "Post Created",
				message: "Your event post has been created successfully.",
				color: "green",
			});
			onClose();
			return response.data;
		} catch (error) {
			console.error("Error creating post:", error);
			if (isAxiosError(error)) {
				notifications.show({
					title: "Error Creating Post",
					message: error.response?.data?.message || error.message,
					color: "red",
				});
			} else {
				notifications.show({
					title: "Error Creating Post",
					message: "There was an error creating your post.",
					color: "red",
				});
			}
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (!opened) {
			setFiles([]);
			setPreviewUrls([]);
			setPreviewKeys([]);
			setUploadedMedia([]);
			form.reset();
		}
	}, [opened, form]);

	return (
		<>
			{loading && <FullScreenLoader />}
			<Modal
				opened={opened}
				onClose={onClose}
				title={
					<Stack gap={0}>
						<Title order={3}>
							{postFormData ? "Update Post" : "Create Post"}
						</Title>
						<Text c={"dimmed"} size="sm">
							Share your thoughts and experiences with the
							community
						</Text>
					</Stack>
				}
				size="xl"
				scrollAreaComponent={ScrollArea.Autosize}
				padding="lg"
				withCloseButton={false}
				trapFocus={false}
				closeOnEscape={false}
			>
				<form onSubmit={handleSubmit}>
					<Stack gap="lg">
						<CustomQuill
							value={form.values.content}
							customStyle={`
              .ql-container.ql-snow {
                border: 1px solid #ccc !important;
                border-top: none !important;
              }
              .ql-editor {
                padding: 12px 15px !important;
                max-height: 280px;
                overflow-y: auto;
              }
            `}
							minHeight="250px"
							onChange={val => form.setFieldValue("content", val)}
							modules={{
								...defaultCustomModules,
								mention: {
									allowedChars: /^[A-Za-z\sÅÄÖåäö]*$/,
									mentionDenotationChars: ["@"],
									source: async function (
										searchTerm: string,
										renderList: (
											list: EventUser[],
											searchTerm: string
										) => void
									) {
										try {
											const response =
												await apiClient.get(
													`/api/events/event-post/mentions?searchTerm=${searchTerm}`
												);

											// Shape data correctly for quill-mention
											const values = response.data.map(
												(user: EventUser) => ({
													id: user._id,
													value: `${user.firstName} ${user.secondName ?? ""}`.trim(),
													email: user.email,
													image: user.image,
												})
											);

											if (searchTerm.length === 0) {
												renderList(values, searchTerm);
											} else {
												const matches = values.filter(
													v =>
														v.value
															.toLowerCase()
															.includes(
																searchTerm.toLowerCase()
															)
												);
												renderList(matches, searchTerm);
											}
										} catch (error) {
											console.error(
												"Mention fetch failed:",
												error
											);
											renderList([], searchTerm);
										}
									},
								},
							}}
						/>

						{previewUrls.length > 0 && (
							<SimpleGrid cols={3}>
								<AnimatePresence>
									{previewUrls.map((url, index) => (
										<ImageItem
											key={previewKeys[index] || index}
											url={url}
											index={index}
											onRemove={handleRemoveFile}
											onPreview={handlePreview}
										/>
									))}
								</AnimatePresence>
							</SimpleGrid>
						)}

						<Tooltip label="Add Media">
							<ActionIcon
								onClick={() => fileInputRef.current?.click()}
								variant="subtle"
							>
								<IconPhoto size={32} />
							</ActionIcon>
						</Tooltip>
						<input
							accept="image/*"
							multiple
							onChange={handleFileChange}
							ref={fileInputRef}
							style={{ display: "none" }}
							type="file"
						/>
					</Stack>

					<Divider mt="xl" />

					<Group justify="flex-end" mt="lg">
						<Button onClick={onClose} variant="outline">
							Cancel
						</Button>
						<Button type="submit" loading={loading}>
							Post
						</Button>
					</Group>
				</form>
			</Modal>

			{viewerOpened !== null && (
				<GalleryViewer
					opened={viewerOpened !== null}
					onClose={() => setViewerOpened(null)}
					images={previewUrls.map(media => media)}
					initial={viewerOpened || 0}
				/>
			)}
		</>
	);
};

export default EventPostFormModal;
