import { modals } from "@mantine/modals";
import { Text, Title } from "@mantine/core";

interface CustomModalProps {
	title?: string;
	description?: string;
	confirmCallback: () => void;
}

export default function openCustomModal({
	title,
	description,
	confirmCallback,
}: CustomModalProps) {
	modals.openConfirmModal({
		title: (
			<Title order={3}>
				{title ? title : "Are you sure you want to proceed?"}
			</Title>
		),
		centered: true,
		children: <Text>{description ?? ""}</Text>,
		labels: { confirm: "Confirm", cancel: "Cancel" },
		onConfirm: confirmCallback,
		trapFocus: false,
	});
}
